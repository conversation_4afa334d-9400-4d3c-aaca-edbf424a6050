import { gql } from '@apollo/client';

export const SET_SAMPLE_DATASETS = gql`
  mutation StartSampleDataset($data: SampleDatasetInput!) {
    startSampleDataset(data: $data)
  }
`;

export const LIST_DATA_SOURCE_TABLES = gql`
  query ListDataSourceTables {
    listDataSourceTables {
      name
      columns {
        name
        type
      }
    }
  }
`;

export const AUTO_GENERATED_RELATIONS = gql`
  query AutoGeneratedRelations {
    autoGenerateRelation {
      id
      displayName
      referenceName
      relations {
        fromModelId
        fromModelReferenceName
        fromColumnId
        fromColumnReferenceName
        toModelId
        toModelReferenceName
        toColumnId
        toColumnReferenceName
        type
        name
      }
    }
  }
`;

export const SAVE_DATA_SOURCE = gql`
  mutation SaveDataSource($data: DataSourceInput!) {
    saveDataSource(data: $data) {
      type
      properties
    }
  }
`;

export const UPDATE_DATA_SOURCE = gql`
  mutation UpdateDataSource($data: UpdateDataSourceInput!) {
    updateDataSource(data: $data) {
      type
      properties
    }
  }
`;

export const SAVE_TABLES = gql`
  mutation SaveTables($data: SaveTablesInput!) {
    saveTables(data: $data)
  }
`;

export const SAVE_RELATIONS = gql`
  mutation SaveRelations($data: SaveRelationInput!) {
    saveRelations(data: $data)
  }
`;

export const GET_SCHEMA_CHANGE = gql`
  query SchemaChange {
    schemaChange {
      deletedTables {
        sourceTableName
        displayName
        columns {
          sourceColumnName
          displayName
          type
        }
        relationships {
          displayName
          referenceName
        }
        calculatedFields {
          displayName
          referenceName
          type
        }
      }
      deletedColumns {
        sourceTableName
        displayName
        columns {
          sourceColumnName
          displayName
          type
        }
        relationships {
          displayName
          referenceName
        }
        calculatedFields {
          displayName
          referenceName
          type
        }
      }
      modifiedColumns {
        sourceTableName
        displayName
        columns {
          sourceColumnName
          displayName
          type
        }
        relationships {
          displayName
          referenceName
        }
        calculatedFields {
          displayName
          referenceName
          type
        }
      }
      lastSchemaChangeTime
    }
  }
`;

export const TRIGGER_DATA_SOURCE_DETECTION = gql`
  mutation TriggerDataSourceDetection {
    triggerDataSourceDetection
  }
`;

export const RESOLVE_SCHEMA_CHANGE = gql`
  mutation ResolveSchemaChange($where: ResolveSchemaChangeWhereInput!) {
    resolveSchemaChange(where: $where)
  }
`;
