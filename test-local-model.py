#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WrenAI本地模型连接测试脚本
用于验证本地Qwen3-235B-A22B模型服务的连接和配置
"""

import requests
import json
import time
import sys
from typing import Dict, Any

# 配置信息
LOCAL_MODEL_CONFIG = {
    "base_url": "http://************:8000/v1",
    "api_key": "sk-or-v1-123456789",
    "model": "Qwen3-235B-A22B",
    "temperature": 0.2,
    "max_tokens": 5000
}

WREN_AI_CONFIG = {
    "ai_service_url": "http://localhost:5555",
    "ui_service_url": "http://localhost:3000"
}

def print_status(message: str, status: str = "info"):
    """打印状态信息"""
    icons = {
        "info": "ℹ️",
        "success": "✅",
        "error": "❌",
        "warning": "⚠️",
        "testing": "🔍"
    }
    print(f"{icons.get(status, 'ℹ️')} {message}")

def test_local_model_connection():
    """测试本地模型服务连接"""
    print_status("测试本地模型服务连接...", "testing")
    
    try:
        # 测试模型列表接口
        response = requests.get(
            f"{LOCAL_MODEL_CONFIG['base_url']}/models",
            headers={"Authorization": f"Bearer {LOCAL_MODEL_CONFIG['api_key']}"},
            timeout=10
        )
        
        if response.status_code == 200:
            models = response.json()
            print_status("本地模型服务连接成功", "success")
            print(f"   可用模型: {json.dumps(models, indent=2, ensure_ascii=False)}")
            return True
        else:
            print_status(f"本地模型服务响应错误: {response.status_code}", "error")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_status(f"本地模型服务连接失败: {e}", "error")
        return False

def test_local_model_chat():
    """测试本地模型对话功能"""
    print_status("测试本地模型对话功能...", "testing")
    
    try:
        payload = {
            "model": LOCAL_MODEL_CONFIG["model"],
            "messages": [
                {"role": "user", "content": "你好，请简单介绍一下你自己。"}
            ],
            "temperature": LOCAL_MODEL_CONFIG["temperature"],
            "max_tokens": 100
        }
        
        response = requests.post(
            f"{LOCAL_MODEL_CONFIG['base_url']}/chat/completions",
            headers={
                "Authorization": f"Bearer {LOCAL_MODEL_CONFIG['api_key']}",
                "Content-Type": "application/json"
            },
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print_status("本地模型对话测试成功", "success")
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                print(f"   模型回复: {content[:100]}...")
            return True
        else:
            print_status(f"本地模型对话测试失败: {response.status_code}", "error")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_status(f"本地模型对话测试异常: {e}", "error")
        return False

def test_wren_ai_service():
    """测试WrenAI服务"""
    print_status("测试WrenAI服务连接...", "testing")
    
    try:
        # 测试AI服务健康检查
        response = requests.get(
            f"{WREN_AI_CONFIG['ai_service_url']}/health",
            timeout=10
        )
        
        if response.status_code == 200:
            print_status("WrenAI服务连接成功", "success")
            return True
        else:
            print_status(f"WrenAI服务响应错误: {response.status_code}", "error")
            return False
            
    except requests.exceptions.RequestException as e:
        print_status(f"WrenAI服务连接失败: {e}", "error")
        return False

def test_wren_ui_service():
    """测试WrenUI服务"""
    print_status("测试WrenUI服务连接...", "testing")
    
    try:
        response = requests.get(
            WREN_AI_CONFIG['ui_service_url'],
            timeout=10
        )
        
        if response.status_code == 200:
            print_status("WrenUI服务连接成功", "success")
            return True
        else:
            print_status(f"WrenUI服务响应错误: {response.status_code}", "error")
            return False
            
    except requests.exceptions.RequestException as e:
        print_status(f"WrenUI服务连接失败: {e}", "error")
        return False

def test_integration():
    """测试集成功能"""
    print_status("测试WrenAI与本地模型集成...", "testing")
    
    try:
        # 这里可以添加更复杂的集成测试
        # 比如通过WrenAI API调用本地模型
        print_status("集成测试需要WrenAI服务完全启动后进行", "warning")
        return True
        
    except Exception as e:
        print_status(f"集成测试失败: {e}", "error")
        return False

def main():
    """主函数"""
    print("🚀 开始WrenAI本地模型配置验证...")
    print("=" * 50)
    
    # 测试结果
    results = {}
    
    # 1. 测试本地模型连接
    results["local_model_connection"] = test_local_model_connection()
    print()
    
    # 2. 测试本地模型对话
    if results["local_model_connection"]:
        results["local_model_chat"] = test_local_model_chat()
    else:
        results["local_model_chat"] = False
        print_status("跳过本地模型对话测试（连接失败）", "warning")
    print()
    
    # 3. 测试WrenAI服务
    results["wren_ai_service"] = test_wren_ai_service()
    print()
    
    # 4. 测试WrenUI服务
    results["wren_ui_service"] = test_wren_ui_service()
    print()
    
    # 5. 测试集成
    if all([results["local_model_connection"], results["wren_ai_service"]]):
        results["integration"] = test_integration()
    else:
        results["integration"] = False
        print_status("跳过集成测试（前置条件不满足）", "warning")
    
    # 输出测试结果
    print("=" * 50)
    print("📊 测试结果汇总:")
    
    for test_name, result in results.items():
        status = "success" if result else "error"
        test_display_name = {
            "local_model_connection": "本地模型连接",
            "local_model_chat": "本地模型对话",
            "wren_ai_service": "WrenAI服务",
            "wren_ui_service": "WrenUI服务",
            "integration": "集成测试"
        }.get(test_name, test_name)
        
        print_status(f"{test_display_name}: {'通过' if result else '失败'}", status)
    
    # 总体结果
    all_passed = all(results.values())
    print()
    if all_passed:
        print_status("🎉 所有测试通过！WrenAI本地模型配置正确", "success")
        print_status("您可以开始使用WrenAI了: http://localhost:3000", "info")
    else:
        print_status("❌ 部分测试失败，请检查配置", "error")
        print_status("请参考部署文档进行故障排除", "info")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
