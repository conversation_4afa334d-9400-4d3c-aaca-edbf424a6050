@echo off
chcp 65001 >nul
echo 🔧 修复WrenAI数据库连接问题
echo.
echo 📋 问题分析:
echo    - PostgreSQL服务器: ************:5432
echo    - Docker容器无法访问外部数据库服务器
echo    - 需要配置Docker网络以允许容器访问外部服务

echo.
echo 🔍 开始修复...

echo.
echo 1️⃣ 测试宿主机到数据库的连通性...
ping -n 2 ************ >nul 2>&1
if errorlevel 1 (
    echo ❌ 宿主机无法ping通数据库服务器
    echo    请检查:
    echo    - 网络连接是否正常
    echo    - IP地址是否正确
    echo    - 防火墙设置
    goto :solution
) else (
    echo ✅ 宿主机可以ping通数据库服务器
)

echo.
echo 2️⃣ 尝试方案1: 使用host网络模式...
echo    修改ibis-server使用host网络模式以直接访问宿主机网络

echo 停止当前服务...
docker-compose -f docker-compose.local-model.yaml stop ibis-server

echo 备份当前配置...
copy docker-compose.local-model.yaml docker-compose.local-model.yaml.backup >nul

echo 创建host网络模式配置...
powershell -Command "(Get-Content docker-compose.local-model.yaml) -replace '    networks:', '    # networks:' -replace '      - wren', '      # - wren' | Set-Content docker-compose.local-model.yaml.tmp"
powershell -Command "(Get-Content docker-compose.local-model.yaml.tmp) -replace '  ibis-server:', ('  ibis-server:' + [Environment]::NewLine + '    network_mode: \"host\"') | Set-Content docker-compose.local-model.yaml"
del docker-compose.local-model.yaml.tmp

echo 重启ibis-server...
docker-compose -f docker-compose.local-model.yaml up -d ibis-server

echo 等待服务启动...
timeout /t 10 /nobreak >nul

echo 测试连接...
docker exec wrenai-ibis-server-1 python3 -c "
import socket
try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(5)
    result = sock.connect_ex(('************', 5432))
    sock.close()
    if result == 0:
        print('✅ host网络模式连接成功')
        exit(0)
    else:
        print('❌ host网络模式连接失败')
        exit(1)
except Exception as e:
    print(f'❌ 连接测试异常: {e}')
    exit(1)
" 2>nul
if errorlevel 1 (
    echo ❌ host网络模式方案失败，恢复原配置...
    copy docker-compose.local-model.yaml.backup docker-compose.local-model.yaml >nul
    docker-compose -f docker-compose.local-model.yaml up -d ibis-server
    goto :solution2
) else (
    echo ✅ host网络模式方案成功！
    goto :success
)

:solution2
echo.
echo 3️⃣ 尝试方案2: 修改Docker网络配置...
echo    添加自定义网络配置以改善连通性

echo 停止所有服务...
docker-compose -f docker-compose.local-model.yaml down

echo 创建自定义网络配置...
docker network create --driver bridge --subnet=**********/16 wren-custom 2>nul

echo 修改配置使用自定义网络...
powershell -Command "
$content = Get-Content docker-compose.local-model.yaml
$newContent = @()
$inNetworks = $false
foreach ($line in $content) {
    if ($line -match '^networks:') {
        $inNetworks = $true
        $newContent += 'networks:'
        $newContent += '  wren:'
        $newContent += '    external: true'
        $newContent += '    name: wren-custom'
        continue
    }
    if ($inNetworks -and $line -match '^services:') {
        $inNetworks = $false
    }
    if (-not $inNetworks) {
        $newContent += $line
    }
}
$newContent | Set-Content docker-compose.local-model.yaml.tmp
"

move docker-compose.local-model.yaml.tmp docker-compose.local-model.yaml

echo 重启服务...
docker-compose -f docker-compose.local-model.yaml up -d

echo 等待服务启动...
timeout /t 15 /nobreak >nul

echo 测试连接...
docker exec wrenai-ibis-server-1 python3 -c "
import socket
try:
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(5)
    result = sock.connect_ex(('************', 5432))
    sock.close()
    if result == 0:
        print('✅ 自定义网络连接成功')
        exit(0)
    else:
        print('❌ 自定义网络连接失败')
        exit(1)
except Exception as e:
    print(f'❌ 连接测试异常: {e}')
    exit(1)
" 2>nul
if errorlevel 1 (
    echo ❌ 自定义网络方案失败，恢复原配置...
    copy docker-compose.local-model.yaml.backup docker-compose.local-model.yaml >nul
    docker network rm wren-custom 2>nul
    docker-compose -f docker-compose.local-model.yaml up -d
    goto :solution
) else (
    echo ✅ 自定义网络方案成功！
    goto :success
)

:solution
echo.
echo 🛠️ 手动解决方案建议:
echo.
echo 方案A: 检查PostgreSQL服务器配置
echo    1. 确保PostgreSQL服务正在运行
echo    2. 检查pg_hba.conf文件，添加以下行:
echo       host all all **********/12 md5
echo    3. 检查postgresql.conf文件:
echo       listen_addresses = '*'
echo    4. 重启PostgreSQL服务
echo.
echo 方案B: 使用SQLite替代PostgreSQL
echo    1. 在WrenAI中配置使用SQLite数据库
echo    2. 无需外部数据库服务器
echo.
echo 方案C: 检查网络和防火墙
echo    1. 确保5432端口在防火墙中开放
echo    2. 检查网络路由配置
echo    3. 尝试从其他机器连接数据库
echo.
goto :end

:success
echo.
echo 🎉 数据库连接问题已解决！
echo.
echo 📝 后续步骤:
echo    1. 访问 http://localhost:3000 测试WrenAI界面
echo    2. 尝试连接数据库或创建数据源
echo    3. 如有其他问题，请查看容器日志
echo.
echo 💡 查看日志命令:
echo    docker logs wrenai-ibis-server-1
echo    docker logs wrenai-wren-ui-1

:end
echo.
echo 清理备份文件...
if exist docker-compose.local-model.yaml.backup del docker-compose.local-model.yaml.backup

pause
