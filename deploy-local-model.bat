@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM WrenAI本地模型部署脚本 (Windows版本)
REM 用于部署连接到本地Qwen3-235B-A22B模型的WrenAI服务

echo 🚀 开始部署WrenAI本地模型服务...

REM 检查必要的文件是否存在
echo 📋 检查配置文件...
if not exist ".env" (
    echo ❌ 错误: .env文件不存在
    pause
    exit /b 1
)

if not exist "config.yaml" (
    echo ❌ 错误: config.yaml文件不存在
    pause
    exit /b 1
)

if not exist "docker-compose.local-model.yaml" (
    echo ❌ 错误: docker-compose.local-model.yaml文件不存在
    pause
    exit /b 1
)

echo ✅ 配置文件检查完成

REM 创建必要的目录
echo 📁 创建必要的目录...
if not exist "data" mkdir data
echo ✅ 目录创建完成

REM 检查Docker是否运行
echo 🔍 检查Docker服务...
docker version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: Docker未运行或未安装
    echo    正在尝试启动Docker Desktop...
    start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    echo    请等待Docker Desktop完全启动后重新运行此脚本
    pause
    exit /b 1
)
echo ✅ Docker服务正常

REM 检查本地模型服务是否可访问
echo 🔍 检查本地模型服务连接...
curl -s --connect-timeout 5 http://************:8000/v1/models >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告: 无法连接到本地模型服务 (http://************:8000)
    echo    请确保您的本地模型服务正在运行
    set /p continue="   是否继续部署? (y/N): "
    if /i not "!continue!"=="y" (
        echo ❌ 部署已取消
        pause
        exit /b 1
    )
) else (
    echo ✅ 本地模型服务连接正常
)

REM 停止现有的服务（如果有的话）
echo 🛑 停止现有服务...
docker-compose -f docker-compose.local-model.yaml down --remove-orphans >nul 2>&1
echo ✅ 现有服务已停止


REM 启动服务
echo 🚀 启动WrenAI服务...
docker-compose -f docker-compose.local-model.yaml up -d
if errorlevel 1 (
    echo ❌ 服务启动失败
    pause
    exit /b 1
)

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo 🔍 检查服务状态...
docker-compose -f docker-compose.local-model.yaml ps

REM 检查AI服务是否正常启动
echo 🔍 检查AI服务健康状态...
set /a counter=0
:check_ai_service
set /a counter+=1
curl -s http://localhost:5555/health >nul 2>&1
if not errorlevel 1 (
    echo ✅ AI服务启动成功
    goto check_ui_service
)
if !counter! geq 30 (
    echo ❌ AI服务启动超时
    echo 请检查日志: docker-compose -f docker-compose.local-model.yaml logs wren-ai-service
    pause
    exit /b 1
)
echo    等待AI服务启动... (!counter!/30)
timeout /t 2 /nobreak >nul
goto check_ai_service

:check_ui_service
REM 检查UI服务是否正常启动
echo 🔍 检查UI服务健康状态...
set /a counter=0
:check_ui_loop
set /a counter+=1
curl -s http://localhost:3000 >nul 2>&1
if not errorlevel 1 (
    echo ✅ UI服务启动成功
    goto deployment_complete
)
if !counter! geq 30 (
    echo ❌ UI服务启动超时
    echo 请检查日志: docker-compose -f docker-compose.local-model.yaml logs wren-ui
    pause
    exit /b 1
)
echo    等待UI服务启动... (!counter!/30)
timeout /t 2 /nobreak >nul
goto check_ui_loop

:deployment_complete
echo.
echo 🎉 WrenAI本地模型服务部署完成!
echo.
echo 📊 服务信息:
echo    - WrenAI UI: http://localhost:3000
echo    - AI服务API: http://localhost:5555
echo    - 本地模型: http://************:8000/v1
echo.
echo 📝 常用命令:
echo    - 查看日志: docker-compose -f docker-compose.local-model.yaml logs -f
echo    - 停止服务: docker-compose -f docker-compose.local-model.yaml down
echo    - 重启服务: docker-compose -f docker-compose.local-model.yaml restart
echo.
echo 🔧 如果遇到问题，请检查:
echo    1. 本地模型服务是否正常运行
echo    2. 网络连接是否正常
echo    3. 配置文件是否正确
echo.
echo 按任意键退出...
pause >nul
