@echo off
chcp 65001 >nul
echo 🔧 修复数据库网络连接问题

echo 📋 当前问题:
echo    - PostgreSQL服务器: ************:5432
echo    - 数据库: yeestor
echo    - 用户: root
echo    - Docker容器无法连接到数据库服务器

echo.
echo 🔍 诊断步骤:

echo 1. 测试宿主机到数据库服务器的连通性...
ping -n 2 ************ >nul 2>&1
if errorlevel 1 (
    echo ❌ 宿主机无法ping通数据库服务器
    echo    请检查网络连接和防火墙设置
) else (
    echo ✅ 宿主机可以ping通数据库服务器
)

echo.
echo 2. 重启ibis-server服务以应用网络配置...
docker-compose -f docker-compose.local-model.yaml restart ibis-server
if errorlevel 1 (
    echo ❌ 重启ibis-server失败
    pause
    exit /b 1
)
echo ✅ ibis-server重启成功

echo.
echo 3. 等待服务完全启动...
timeout /t 10 /nobreak >nul

echo.
echo 4. 检查容器网络配置...
docker exec wrenai-ibis-server-1 cat /etc/hosts | findstr "************"
if errorlevel 1 (
    echo ❌ 容器内没有找到数据库服务器的hosts映射
) else (
    echo ✅ 容器内已配置数据库服务器的hosts映射
)

echo.
echo 📝 解决方案建议:
echo.
echo 方案1: 检查PostgreSQL服务器配置
echo    - 确保PostgreSQL服务正在运行
echo    - 检查pg_hba.conf文件，允许来自Docker网络的连接
echo    - 检查postgresql.conf文件，确保listen_addresses包含'*'或具体IP
echo.
echo 方案2: 检查防火墙设置
echo    - 确保5432端口在防火墙中开放
echo    - 检查Windows防火墙和任何网络防火墙
echo.
echo 方案3: 使用host网络模式
echo    - 修改docker-compose.yaml使用host网络模式
echo.
echo 方案4: 使用不同的数据库
echo    - 在WrenAI中配置其他可访问的数据库
echo    - 或者使用SQLite（无需外部数据库）

echo.
echo 🔧 立即尝试的修复:
set /p choice="是否尝试使用host网络模式重启服务? (y/N): "
if /i "%choice%"=="y" (
    echo 正在使用host网络模式重启...
    docker-compose -f docker-compose.local-model.yaml down
    echo 请手动编辑docker-compose.local-model.yaml文件
    echo 在ibis-server服务中添加: network_mode: "host"
    pause
) else (
    echo 请根据上述建议检查数据库服务器配置
)

echo.
echo 💡 提示: 如果问题持续存在，请:
echo    1. 检查数据库服务器日志
echo    2. 确认数据库用户权限
echo    3. 测试从其他机器连接数据库
echo    4. 考虑使用本地SQLite数据库进行测试

pause
