# WrenAI本地模型部署 Makefile
# 提供便捷的命令来管理WrenAI本地模型部署

.PHONY: help deploy stop restart logs test clean status

# 默认目标
help:
	@echo "WrenAI本地模型部署管理命令:"
	@echo ""
	@echo "  make deploy    - 部署WrenAI服务"
	@echo "  make stop      - 停止所有服务"
	@echo "  make restart   - 重启所有服务"
	@echo "  make logs      - 查看服务日志"
	@echo "  make test      - 运行连接测试"
	@echo "  make status    - 查看服务状态"
	@echo "  make clean     - 清理所有数据和容器"
	@echo ""
	@echo "本地模型配置:"
	@echo "  - 模型: Qwen3-235B-A22B"
	@echo "  - 地址: http://************:8000/v1"
	@echo "  - UI: http://localhost:3000"

# 部署服务
deploy:
	@echo "🚀 开始部署WrenAI本地模型服务..."
	@if [ ! -f ".env" ]; then echo "❌ .env文件不存在"; exit 1; fi
	@if [ ! -f "config.yaml" ]; then echo "❌ config.yaml文件不存在"; exit 1; fi
	@mkdir -p data
	docker-compose -f docker-compose.local-model.yaml down --remove-orphans
	docker-compose -f docker-compose.local-model.yaml pull
	docker-compose -f docker-compose.local-model.yaml up -d
	@echo "✅ 服务部署完成"
	@echo "📊 访问地址: http://localhost:3000"

# 停止服务
stop:
	@echo "🛑 停止WrenAI服务..."
	docker-compose -f docker-compose.local-model.yaml down
	@echo "✅ 服务已停止"

# 重启服务
restart:
	@echo "🔄 重启WrenAI服务..."
	docker-compose -f docker-compose.local-model.yaml restart
	@echo "✅ 服务已重启"

# 查看日志
logs:
	@echo "📋 查看服务日志..."
	docker-compose -f docker-compose.local-model.yaml logs -f

# 查看服务状态
status:
	@echo "📊 服务状态:"
	docker-compose -f docker-compose.local-model.yaml ps

# 运行测试
test:
	@echo "🔍 运行连接测试..."
	python3 test-local-model.py

# 清理所有数据
clean:
	@echo "🧹 清理所有数据和容器..."
	docker-compose -f docker-compose.local-model.yaml down -v --remove-orphans
	docker system prune -f
	@echo "✅ 清理完成"

# 查看AI服务日志
logs-ai:
	@echo "📋 查看AI服务日志..."
	docker-compose -f docker-compose.local-model.yaml logs -f wren-ai-service

# 查看UI服务日志
logs-ui:
	@echo "📋 查看UI服务日志..."
	docker-compose -f docker-compose.local-model.yaml logs -f wren-ui

# 进入AI服务容器
shell-ai:
	@echo "🐚 进入AI服务容器..."
	docker-compose -f docker-compose.local-model.yaml exec wren-ai-service bash

# 测试本地模型连接
test-model:
	@echo "🔍 测试本地模型连接..."
	curl -X GET "http://************:8000/v1/models" \
		-H "Authorization: Bearer sk-or-v1-123456789" \
		-H "Content-Type: application/json" | jq .

# 测试WrenAI健康状态
test-health:
	@echo "🔍 测试WrenAI服务健康状态..."
	@echo "AI服务:"
	@curl -s http://localhost:5555/health || echo "❌ AI服务不可用"
	@echo ""
	@echo "UI服务:"
	@curl -s http://localhost:3000 > /dev/null && echo "✅ UI服务正常" || echo "❌ UI服务不可用"
