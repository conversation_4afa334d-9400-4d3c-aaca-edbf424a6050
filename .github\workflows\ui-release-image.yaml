name: Wren-UI Release image

on:
  workflow_dispatch:
    inputs:
      docker_image_tag_name:
        description: Docker image tag name (optional, default branch name/tag name replaced with hyphens)
        type: string

env:
  WREN_UI_IMAGE: ghcr.io/canner/wren-ui

defaults:
  run:
    working-directory: wren-ui

jobs:
  build-image:
    outputs:
      tag_name: ${{ steps.tag-preparation.outputs.TAG_NAME }}
    strategy:
      fail-fast: false
      matrix:
        arch:
          - runner: ubuntu-latest
            platform: linux/amd64
          - runner: linux_arm64_runner
            platform: linux/arm64
    runs-on: ${{ matrix.arch.runner }}
    steps:
      - uses: actions/checkout@v4
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Prepare tag name
        id: tag-preparation
        run: |
          if [ -n "${{ github.event.inputs.docker_image_tag_name }}" ]; then
            tag_name=${{ github.event.inputs.docker_image_tag_name }}
          else
            tag_name=$(echo ${{ github.ref_name }} | sed 's/[^a-zA-Z0-9]/-/g')-$(git log -1 --pretty=%h)
          fi
          echo "TAG_NAME=$tag_name" >> $GITHUB_OUTPUT
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Prepare platform
        run: |
          platform=${{ matrix.arch.platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV
      - name: Build and push by digest
        id: build
        uses: docker/build-push-action@v6
        with:
          platforms: ${{ matrix.arch.platform }}
          labels: ${{ env.WREN_UI_IMAGE }}
          context: ./wren-ui
          outputs: type=image,name=${{ env.WREN_UI_IMAGE }},push-by-digest=true,name-canonical=true,push=true
      - name: Export digest
        run: |
          mkdir -p /tmp/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "/tmp/digests/${digest#sha256:}"
      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: /tmp/digests/*
          if-no-files-found: error
          retention-days: 1
  merge:
    runs-on: ubuntu-latest
    needs: [ build-image ]
    steps:
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: /tmp/digests
          pattern: digests-*
          merge-multiple: true
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.WREN_UI_IMAGE }}
          tags: |
            ${{ needs.build-image.outputs.tag_name }}
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Create manifest list and push
        working-directory: /tmp/digests
        run: |
          TAGS=$(echo "${{ steps.meta.outputs.tags }}" | awk '{printf "--tag %s ", $0}')
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf '${{ env.WREN_UI_IMAGE }}@sha256:%s ' *) \
            $TAGS
