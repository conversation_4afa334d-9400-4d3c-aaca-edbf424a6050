export const EditSVG = ({
  fillCurrentColor = true,
  className,
}: {
  fillCurrentColor?: boolean;
  className?: string;
}) => (
  <svg
    className={className}
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.9929 5.04652L10.3083 5.7311L11.602 7.0247L12.2867 6.34012C12.2652 6.26694 12.2367 6.18523 12.2012 6.0998C12.1016 5.86038 11.968 5.64205 11.8296 5.5036C11.6911 5.36514 11.4727 5.23157 11.2333 5.13201C11.1479 5.09648 11.0661 5.06797 10.9929 5.04652ZM10.6592 7.96748L9.36549 6.67388L5.92291 10.1162C5.50551 10.5335 5.25873 11.2125 5.12676 11.8724C5.09852 12.0136 5.07652 12.1489 5.0594 12.2738C5.18415 12.2566 5.31921 12.2346 5.46023 12.2064C6.11984 12.0744 6.79883 11.8275 7.21669 11.4097L10.6592 7.96748ZM4.33325 13C3.66659 13 3.66659 12.9996 3.66659 12.9996L3.66659 12.9983L3.66659 12.9957L3.66664 12.9882L3.66695 12.9637C3.66728 12.9432 3.66793 12.9146 3.66917 12.8787C3.67164 12.807 3.67649 12.7058 3.68602 12.5818C3.70502 12.3349 3.74295 11.9928 3.81931 11.611C3.96692 10.8728 4.27927 9.87414 4.98014 9.17333L10.2919 3.8619C10.4156 3.73825 10.5829 3.66813 10.7578 3.66667C11.0504 3.66422 11.4208 3.76595 11.7452 3.90087C12.0828 4.04124 12.47 4.25846 12.7723 4.56075C13.0747 4.86305 13.2919 5.25027 13.4323 5.58782C13.5672 5.91229 13.669 6.28262 13.6665 6.57528C13.6651 6.75017 13.5949 6.91748 13.4713 7.04115L8.15946 12.3525C7.45837 13.0536 6.45995 13.3661 5.72188 13.5138C5.34011 13.5902 4.99815 13.6282 4.75126 13.6472C4.62736 13.6567 4.5262 13.6616 4.4545 13.6641C4.41863 13.6653 4.39004 13.6659 4.36957 13.6663L4.34504 13.6666L4.33751 13.6666L4.33497 13.6666L4.33401 13.6666C4.33401 13.6666 4.33325 13.6666 4.33325 13ZM4.33325 13V13.6666C3.96506 13.6666 3.66659 13.3678 3.66659 12.9996L4.33325 13Z"
      fill={fillCurrentColor ? 'currentColor' : undefined}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.99992 0.333313C4.28687 0.333313 4.54163 0.516933 4.63237 0.789161L5.27697 2.72293L7.21074 3.36752C7.48297 3.45827 7.66659 3.71303 7.66659 3.99998C7.66659 4.28693 7.48297 4.54169 7.21074 4.63244L5.27697 5.27703L4.63237 7.2108C4.54163 7.48303 4.28687 7.66665 3.99992 7.66665C3.71297 7.66665 3.45821 7.48303 3.36746 7.2108L2.72287 5.27703L0.7891 4.63244C0.516872 4.54169 0.333252 4.28693 0.333252 3.99998C0.333252 3.71303 0.516872 3.45827 0.7891 3.36752L2.72287 2.72293L3.36746 0.789161C3.45821 0.516933 3.71297 0.333313 3.99992 0.333313ZM3.99992 3.10816L3.88237 3.4608C3.81602 3.65987 3.65981 3.81608 3.46074 3.88244L3.1081 3.99998L3.46074 4.11752C3.65981 4.18388 3.81602 4.34009 3.88237 4.53916L3.99992 4.89179L4.11746 4.53916C4.18382 4.34009 4.34003 4.18388 4.5391 4.11752L4.89173 3.99998L4.5391 3.88244C4.34003 3.81608 4.18382 3.65987 4.11746 3.4608L3.99992 3.10816Z"
      fill={fillCurrentColor ? 'currentColor' : undefined}
    />
  </svg>
);
