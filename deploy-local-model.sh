#!/bin/bash

# WrenAI本地模型部署脚本
# 用于部署连接到本地Qwen3-235B-A22B模型的WrenAI服务

set -e

echo "🚀 开始部署WrenAI本地模型服务..."

# 检查必要的文件是否存在
echo "📋 检查配置文件..."
if [ ! -f ".env" ]; then
    echo "❌ 错误: .env文件不存在"
    exit 1
fi

if [ ! -f "config.yaml" ]; then
    echo "❌ 错误: config.yaml文件不存在"
    exit 1
fi

if [ ! -f "docker-compose.local-model.yaml" ]; then
    echo "❌ 错误: docker-compose.local-model.yaml文件不存在"
    exit 1
fi

echo "✅ 配置文件检查完成"

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p data
echo "✅ 目录创建完成"

# 检查本地模型服务是否可访问
echo "🔍 检查本地模型服务连接..."
if curl -s --connect-timeout 5 http://************:8000/v1/models > /dev/null; then
    echo "✅ 本地模型服务连接正常"
else
    echo "⚠️  警告: 无法连接到本地模型服务 (http://************:8000)"
    echo "   请确保您的本地模型服务正在运行"
    read -p "   是否继续部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 部署已取消"
        exit 1
    fi
fi

# 停止现有的服务（如果有的话）
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.local-model.yaml down --remove-orphans 2>/dev/null || true
echo "✅ 现有服务已停止"

# 拉取最新的镜像
echo "📥 拉取Docker镜像..."
docker-compose -f docker-compose.local-model.yaml pull
echo "✅ 镜像拉取完成"

# 启动服务
echo "🚀 启动WrenAI服务..."
docker-compose -f docker-compose.local-model.yaml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose.local-model.yaml ps

# 检查AI服务是否正常启动
echo "🔍 检查AI服务健康状态..."
for i in {1..30}; do
    if curl -s http://localhost:5555/health > /dev/null 2>&1; then
        echo "✅ AI服务启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ AI服务启动超时"
        echo "请检查日志: docker-compose -f docker-compose.local-model.yaml logs wren-ai-service"
        exit 1
    fi
    echo "   等待AI服务启动... ($i/30)"
    sleep 2
done

# 检查UI服务是否正常启动
echo "🔍 检查UI服务健康状态..."
for i in {1..30}; do
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        echo "✅ UI服务启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ UI服务启动超时"
        echo "请检查日志: docker-compose -f docker-compose.local-model.yaml logs wren-ui"
        exit 1
    fi
    echo "   等待UI服务启动... ($i/30)"
    sleep 2
done

echo ""
echo "🎉 WrenAI本地模型服务部署完成!"
echo ""
echo "📊 服务信息:"
echo "   - WrenAI UI: http://localhost:3000"
echo "   - AI服务API: http://localhost:5555"
echo "   - 本地模型: http://************:8000/v1"
echo ""
echo "📝 常用命令:"
echo "   - 查看日志: docker-compose -f docker-compose.local-model.yaml logs -f"
echo "   - 停止服务: docker-compose -f docker-compose.local-model.yaml down"
echo "   - 重启服务: docker-compose -f docker-compose.local-model.yaml restart"
echo ""
echo "🔧 如果遇到问题，请检查:"
echo "   1. 本地模型服务是否正常运行"
echo "   2. 网络连接是否正常"
echo "   3. 配置文件是否正确"
echo ""
