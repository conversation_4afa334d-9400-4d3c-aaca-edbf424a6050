---
apiVersion: v1
kind: Secret
metadata:
  name: wrenai-secrets
type: Opaque
data:
  # OPENAI_API_KEY is REQUIRED: without a valid key the wren-ai-service-deployment pod will not start
  OPENAI_API_KEY: UkVRVUlSRUQ6IHNrLXByb2otYWxsLWFjY2Vzcy1wbGFjZWhvbGRlci00LXdyZW4tYWktc2VydmljZS1kZXBsb3ltZW50

  # Azure openai env
  AZURE_CHAT_BASE: bi9h
  AZURE_CHAT_KEY: bi9h
  AZURE_CHAT_VERSION: bi9h

  AZURE_EMBED_BASE: bi9h
  AZURE_EMBED_KEY: bi9h
  AZURE_EMBED_VERSION: bi9h

  # Langfuse: for LLM tracing
  LANGFUSE_PUBLIC_KEY: xxxx
  LANGFUSE_SECRET_KEY: xxxx

  ### postgres://        This is the protocol. It tells the system that you’re connecting to a PostgreSQL database.
  ### postgres:postgres  These are the username and password for the database, separated by a colon. In this case, both the username and password are “postgres”.
  ### @wren-postgresql   This is the hostname of the database server. "wren-postgresql" means the database server is running in a Kubernetes cluster and it is named "wren-postgresql" in the *same* namespace. If you are using another namespace you must provide the full hostname, example: `wren-postgresql.wrenai.svc.cluster.local`, "wrenai" is the namespace name, "svc.cluster.local" is the default domain name for Kubernetes services no need to change it.
  ### :5432              This is the port number. PostgreSQL servers listen on port 5432 by default.
  ### /admin_ui          This is the name of the database you’re connecting to. In this case, the database name is “admin_ui”. It can be found in the helm values file in the auth.database parameter (deployment/kustomizations/helm-values_postgresql_14.yaml)
  ### PG_URL: "*************************************************/admin_ui"
  #Fix
  PG_URL: cG9zdGdyZXM6Ly9wb3N0Z3Jlczpwb3N0Z3Jlc0B3cmVuLXBvc3RncmVzcWw6NTQzMi9hZG1pbl91aQo=
  PG_USERNAME: cG9zdGdyZXM=

  POSTHOG_API_KEY: cGhjX2tleS1wbGFjZWhvbGRlcg==
  POSTHOG_HOST: aHR0cHM6Ly9hcHAucG9zdGhvZy5jb20=
  USER_UUID: MDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAw
---
apiVersion: v1
kind: Secret
metadata:
  name: wrenai-postgresql
data:
  postgres-password: cG9zdGdyZXM=
type: Opaque
