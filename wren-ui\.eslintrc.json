{"root": true, "parser": "@typescript-eslint/parser", "extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "prettier"], "plugins": ["@typescript-eslint", "eslint-plugin-prettier"], "ignorePatterns": ["!**/*", ".next/**/*", "src/apollo/client/graphql/__types__.ts", "src/apollo/client/graphql/*.generated.ts"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@next/next/no-html-link-for-pages": ["error", "src/pages"], "@next/next/no-img-element": "off", "react-hooks/exhaustive-deps": "off"}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "rules": {}}], "rules": {"prettier/prettier": "error", "@next/next/no-html-link-for-pages": "off", "react/display-name": 0, "react/no-unescaped-entities": 0, "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-unused-vars": ["error", {"args": "all", "argsIgnorePattern": "^_", "caughtErrors": "all", "caughtErrorsIgnorePattern": "^_", "destructuredArrayIgnorePattern": "^_", "varsIgnorePattern": "^_", "ignoreRestSiblings": true}]}, "env": {"jest": true}}