export const RobotSVG = ({
  fillCurrentColor = true,
  className,
}: {
  fillCurrentColor?: boolean;
  className?: string;
}) => (
  <svg
    className={className}
    width="14"
    height="14"
    viewBox="0 0 17 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.21025 5.02381H11.0755C11.8038 5.0238 12.4049 5.02379 12.8945 5.06379C13.4031 5.10535 13.8706 5.19452 14.3097 5.41826C14.9906 5.76523 15.5443 6.31888 15.8913 6.99984C16.115 7.43896 16.2042 7.90645 16.2457 8.41501C16.2857 8.90466 16.2857 9.50574 16.2857 10.234V14.2898C16.2857 15.0181 16.2857 15.6191 16.2457 16.1088C16.2042 16.6174 16.115 17.0849 15.8913 17.524C15.5443 18.2049 14.9906 18.7586 14.3097 19.1055C13.8706 19.3293 13.4031 19.4185 12.8945 19.46C12.4049 19.5 11.8038 19.5 11.0754 19.5H5.21027C4.48196 19.5 3.88086 19.5 3.39121 19.46C2.88264 19.4185 2.41515 19.3293 1.97603 19.1055C1.29507 18.7586 0.741424 18.2049 0.394453 17.524C0.170714 17.0849 0.0815369 16.6174 0.0399855 16.1088C-2.06409e-05 15.6191 -1.11504e-05 15.0181 3.90216e-07 14.2897V10.2341C-1.11504e-05 9.50576 -2.06409e-05 8.90467 0.0399855 8.41502C0.0815369 7.90645 0.170714 7.43896 0.394453 6.99984C0.741424 6.31888 1.29507 5.76523 1.97603 5.41826C2.41515 5.19452 2.88264 5.10535 3.39121 5.06379C3.88086 5.02379 4.48195 5.0238 5.21025 5.02381ZM3.53856 6.86731C3.1419 6.89972 2.93905 6.95846 2.79754 7.03056C2.45706 7.20404 2.18024 7.48087 2.00675 7.82135C1.93465 7.96285 1.87591 8.16571 1.8435 8.56237C1.81023 8.96959 1.80952 9.49643 1.80952 10.2714V14.2524C1.80952 15.0274 1.81023 15.5542 1.8435 15.9614C1.87591 16.3581 1.93465 16.561 2.00675 16.7025C2.18024 17.0429 2.45706 17.3198 2.79754 17.4932C2.93905 17.5653 3.1419 17.6241 3.53856 17.6565C3.94578 17.6898 4.47262 17.6905 5.24762 17.6905H11.0381C11.8131 17.6905 12.3399 17.6898 12.7472 17.6565C13.1438 17.6241 13.3467 17.5653 13.4882 17.4932C13.8287 17.3198 14.1055 17.0429 14.279 16.7025C14.3511 16.561 14.4098 16.3581 14.4422 15.9614C14.4755 15.5542 14.4762 15.0274 14.4762 14.2524V10.2714C14.4762 9.49643 14.4755 8.96959 14.4422 8.56237C14.4098 8.16571 14.3511 7.96285 14.279 7.82135C14.1055 7.48087 13.8287 7.20404 13.4882 7.03056C13.3467 6.95846 13.1438 6.89972 12.7472 6.86731C12.3399 6.83404 11.8131 6.83333 11.0381 6.83333H5.24762C4.47262 6.83333 3.94578 6.83404 3.53856 6.86731Z"
      fill={fillCurrentColor ? 'currentColor' : undefined}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.33337 2.69857L6.33333 3.21434C6.3333 3.71403 5.9282 4.11908 5.42852 4.11905C4.92883 4.11902 4.52378 3.71392 4.52381 3.21423L4.52387 2.30947C4.52388 2.07611 4.61406 1.85178 4.77557 1.68334C4.99499 1.4545 5.32229 1.12171 5.89443 0.873475C6.44775 0.6334 7.16129 0.5 8.1429 0.5C9.12451 0.5 9.83805 0.63341 10.3914 0.873493C10.9635 1.12173 11.2908 1.45452 11.5102 1.68332C11.6717 1.85177 11.7619 2.07613 11.7619 2.30952V3.21429C11.7619 3.71397 11.3568 4.11905 10.8571 4.11905C10.3575 4.11905 9.95238 3.71397 9.95238 3.21429V2.69856C9.8723 2.63607 9.78449 2.58269 9.67111 2.53349C9.4179 2.42363 8.97077 2.30952 8.1429 2.30952C7.31502 2.30952 6.86788 2.42362 6.61467 2.53348C6.50127 2.58269 6.41345 2.63607 6.33337 2.69857Z"
      fill={fillCurrentColor ? 'currentColor' : undefined}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.70476 13.5286C5.00457 13.1288 5.57168 13.0478 5.97143 13.3476C6.70123 13.895 7.4087 14.0718 8.14237 14.0714C8.87603 14.071 9.58508 13.8945 10.3143 13.3476C10.714 13.0478 11.2811 13.1288 11.581 13.5286C11.8808 13.9283 11.7997 14.4954 11.4 14.7952C10.3197 15.6055 9.21764 15.8804 8.14335 15.881C7.06593 15.8815 5.96543 15.605 4.88572 14.7952C4.48597 14.4954 4.40495 13.9283 4.70476 13.5286Z"
      fill={fillCurrentColor ? 'currentColor' : undefined}
    />
    <path
      d="M3.61905 10.9048C3.61905 10.1552 4.22666 9.54762 4.97619 9.54762C5.72572 9.54762 6.33333 10.1552 6.33333 10.9048C6.33333 11.6543 5.72572 12.2619 4.97619 12.2619C4.22666 12.2619 3.61905 11.6543 3.61905 10.9048Z"
      fill={fillCurrentColor ? 'currentColor' : undefined}
    />
    <path
      d="M9.95238 10.9048C9.95238 10.1552 10.56 9.54762 11.3095 9.54762C12.0591 9.54762 12.6667 10.1552 12.6667 10.9048C12.6667 11.6543 12.0591 12.2619 11.3095 12.2619C10.56 12.2619 9.95238 11.6543 9.95238 10.9048Z"
      fill={fillCurrentColor ? 'currentColor' : undefined}
    />
  </svg>
);
