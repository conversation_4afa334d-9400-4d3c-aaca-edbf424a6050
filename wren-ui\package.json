{"name": "wren-ui", "version": "0.30.0", "private": true, "scripts": {"dev": "TZ=UTC next dev", "build": "NODE_OPTIONS=--max-old-space-size=8192 next build", "start": "TZ=UTC next start", "lint": "yarn check-types && next lint", "test": "jest", "test:e2e": "npx playwright install chromium && npx playwright test", "check-types": "tsc --noEmit", "migrate": "yarn knex migrate:latest", "rollback": "yarn knex migrate:rollback", "generate-gql": "yarn graphql-codegen --config codegen.yaml"}, "dependencies": {"@google-cloud/bigquery": "^6.0.3", "@google-cloud/storage": "^6.10.1", "apollo-server-micro": "^3.10.2", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "better-sqlite3": "^9.4.3", "cron-parser": "^5.1.1", "graphql": "^16.6.0", "graphql-type-json": "^0.3.2", "knex": "^3.1.0", "lodash": "^4.17.21", "log4js": "^6.9.1", "micro": "^9.4.1", "micro-cors": "^0.1.1", "next": "14.2.30", "pg": "^8.8.0", "pg-cursor": "^2.7.4", "posthog-node": "^4.3.2", "sql-formatter": "^15.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@apollo/client": "^3.6.9", "@graphql-codegen/cli": "2.12.0", "@graphql-codegen/introspection": "2.2.1", "@graphql-codegen/near-operation-file-preset": "^2.4.1", "@graphql-codegen/typescript": "2.7.3", "@graphql-codegen/typescript-operations": "^2.5.3", "@graphql-codegen/typescript-react-apollo": "3.3.3", "@next/bundle-analyzer": "^15.3.0", "@playwright/test": "^1.44.0", "@testing-library/react": "14.0.0", "@types/jest": "29.4.4", "@types/lodash": "^4.14.202", "@types/micro-cors": "^0.1.5", "@types/node": "18.16.9", "@types/pg": "^8.6.5", "@types/pg-cursor": "^2.7.0", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@types/react-grid-layout": "^1.3.5", "@types/styled-components": "5.1.26", "@typescript-eslint/eslint-plugin": "6.18.0", "@typescript-eslint/parser": "6.18.0", "ace-builds": "^1.32.3", "antd": "4.20.4", "clsx": "^2.1.1", "cron-parser": "^5.1.1", "dayjs": "^1.11.11", "driver.js": "^1.3.1", "duckdb": "^0.10.1", "duckdb-async": "^0.10.0", "eslint": "^8", "eslint-config-next": "14.2.21", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "29.4.3", "less": "^4.2.0", "less-loader": "^12.2.0", "next-with-less": "^3.0.1", "posthog-js": "^1.205.0", "prettier": "^3.2.5", "react": "18.2.0", "react-ace": "^10.1.0", "react-dom": "18.2.0", "react-grid-layout": "^1.5.0", "react-markdown": "^9.0.1", "reactflow": "11.10.3", "remark-gfm": "^4.0.0", "styled-components": "5.3.6", "styled-icons": "^10.47.0", "ts-essentials": "^9.1.2", "ts-jest": "29.1.1", "ts-node": "9.1.1", "typescript": "5.2.2", "vega": "^5.31.0", "vega-embed": "^6.29.0", "vega-lite": "^5.21.0"}, "resolutions": {"ws": "8.17.1", "axios": "1.8.4", "tar-fs": "2.1.3"}, "_moduleAliases": {"@server": "src/apollo/server"}, "packageManager": "yarn@4.5.3"}