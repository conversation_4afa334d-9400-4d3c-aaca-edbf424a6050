#!/usr/bin/env python3
import socket
import sys

def test_connection(host, port, name):
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        if result == 0:
            print(f'✅ {name}: {host}:{port} 连接成功')
            return True
        else:
            print(f'❌ {name}: {host}:{port} 连接失败 (错误码: {result})')
            return False
    except Exception as e:
        print(f'❌ {name}: {host}:{port} 异常: {e}')
        return False

print('🔍 网络连通性测试:')
print('=' * 40)

# 测试PostgreSQL服务器
test_connection('************', 5432, 'PostgreSQL服务器')

# 测试宿主机
test_connection('***********', 22, '宿主机SSH')

# 测试网关
test_connection('**********', 22, 'Docker网关')
