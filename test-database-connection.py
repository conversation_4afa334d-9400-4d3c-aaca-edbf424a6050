#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库连接测试脚本
用于测试PostgreSQL数据库连接是否正常
"""

import psycopg2
import socket
import time
import sys
from urllib.parse import urlparse

def print_status(message: str, status: str = "info"):
    """打印状态信息"""
    icons = {
        "info": "ℹ️",
        "success": "✅",
        "error": "❌",
        "warning": "⚠️",
        "testing": "🔍"
    }
    print(f"{icons.get(status, 'ℹ️')} {message}")

def test_network_connectivity(host: str, port: int, timeout: int = 5):
    """测试网络连通性"""
    print_status(f"测试网络连通性: {host}:{port}", "testing")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print_status(f"网络连接正常: {host}:{port}", "success")
            return True
        else:
            print_status(f"网络连接失败: {host}:{port} (错误代码: {result})", "error")
            return False
    except Exception as e:
        print_status(f"网络测试异常: {e}", "error")
        return False

def test_postgres_connection(connection_url: str):
    """测试PostgreSQL数据库连接"""
    print_status("测试PostgreSQL数据库连接", "testing")
    
    try:
        # 解析连接URL
        parsed = urlparse(connection_url)
        host = parsed.hostname
        port = parsed.port or 5432
        database = parsed.path.lstrip('/')
        username = parsed.username
        password = parsed.password
        
        print_status(f"连接参数: {username}@{host}:{port}/{database}", "info")
        
        # 先测试网络连通性
        if not test_network_connectivity(host, port):
            return False
        
        # 尝试连接数据库
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=username,
            password=password,
            connect_timeout=10
        )
        
        # 测试查询
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        
        print_status("数据库连接成功", "success")
        print_status(f"PostgreSQL版本: {version}", "info")
        
        cursor.close()
        conn.close()
        return True
        
    except psycopg2.OperationalError as e:
        print_status(f"数据库连接失败: {e}", "error")
        return False
    except Exception as e:
        print_status(f"连接测试异常: {e}", "error")
        return False

def test_from_docker_container():
    """从Docker容器内测试连接"""
    print_status("从Docker容器内测试数据库连接", "testing")
    
    connection_url = "*******************************************/yeestor"
    
    try:
        # 使用docker exec在ibis-server容器内执行测试
        import subprocess
        
        test_command = f"""
python3 -c "
import psycopg2
try:
    conn = psycopg2.connect('{connection_url}')
    print('✅ 容器内数据库连接成功')
    conn.close()
except Exception as e:
    print(f'❌ 容器内数据库连接失败: {{e}}')
"
"""
        
        result = subprocess.run([
            "docker", "exec", "wrenai-ibis-server-1", 
            "sh", "-c", test_command
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print_status("容器内连接测试成功", "success")
            print(result.stdout)
            return True
        else:
            print_status("容器内连接测试失败", "error")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print_status("容器内连接测试超时", "error")
        return False
    except Exception as e:
        print_status(f"容器内测试异常: {e}", "error")
        return False

def main():
    """主函数"""
    print("🔍 开始数据库连接诊断...")
    print("=" * 50)
    
    # 测试的连接URL（从日志中提取）
    connection_url = "*******************************************/yeestor"
    
    print_status(f"测试连接: {connection_url}", "info")
    print()
    
    # 1. 从宿主机测试连接
    print_status("1. 从宿主机测试连接", "info")
    host_result = test_postgres_connection(connection_url)
    print()
    
    # 2. 从Docker容器内测试连接
    print_status("2. 从Docker容器内测试连接", "info")
    container_result = test_from_docker_container()
    print()
    
    # 输出诊断结果
    print("=" * 50)
    print("📊 诊断结果:")
    print_status(f"宿主机连接: {'通过' if host_result else '失败'}", 
                "success" if host_result else "error")
    print_status(f"容器内连接: {'通过' if container_result else '失败'}", 
                "success" if container_result else "error")
    
    print()
    if not host_result and not container_result:
        print_status("🔧 建议的解决方案:", "warning")
        print("   1. 检查PostgreSQL服务器是否正在运行")
        print("   2. 检查防火墙设置，确保5432端口开放")
        print("   3. 检查PostgreSQL配置文件pg_hba.conf和postgresql.conf")
        print("   4. 确认网络连通性和DNS解析")
        print("   5. 检查用户名和密码是否正确")
    elif host_result and not container_result:
        print_status("🔧 建议的解决方案:", "warning")
        print("   1. 检查Docker网络配置")
        print("   2. 添加extra_hosts映射")
        print("   3. 重启Docker服务")
    elif not host_result and container_result:
        print_status("🔧 建议的解决方案:", "warning")
        print("   1. 检查宿主机网络配置")
        print("   2. 检查防火墙设置")
    else:
        print_status("🎉 数据库连接正常！", "success")
    
    return 0 if (host_result or container_result) else 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print_status("\n用户中断测试", "warning")
        sys.exit(1)
    except Exception as e:
        print_status(f"测试脚本异常: {e}", "error")
        sys.exit(1)
