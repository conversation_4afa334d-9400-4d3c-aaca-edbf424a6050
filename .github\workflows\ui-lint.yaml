# create the github action to run the yarn lint when PR created or pushed to main branch
#

name: <PERSON>-<PERSON><PERSON>t

on:
  pull_request:
    types: [ labeled, synchronize ]

permissions:
  contents: read

concurrency:
  # avoid mis-canceling the ci runs while other labels are added to the PR, so we add the label name as the condition
  group: ${{ github.workflow }}-${{ github.event_name == 'pull_request' && github.event.label.name == 'ci/ui' && github.event.number || github.sha }}
  cancel-in-progress: true

defaults:
  run:
    working-directory: wren-ui

jobs:
  eslint:
     # run this job only if the PR is labeled with "ci/ui"
    if: ${{ github.event.label.name == 'ci/ui' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"
      - uses: actions/cache@v2
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: | # should cache node_modules as well
            ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-
      - if: ${{ steps.yarn-cache.outputs.cache-hit != 'true' }}
        name: List the state of node modules
        continue-on-error: true
        run: yarn list
      - name: Install Node.js dependencies
        run: yarn install
      - name: Run lint
        run: yarn lint
          