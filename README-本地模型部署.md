# WrenAI本地模型部署指南

本文档详细说明如何配置和部署WrenAI项目，使其连接到本地部署的Qwen3-235B-A22B模型。

## 📋 前置条件

### 系统要求
- Docker 和 Docker Compose
- 至少8GB可用内存
- 稳定的网络连接

### 本地模型服务
- 模型名称：Qwen3-235B-A22B
- 服务地址：http://************:8000/v1
- API密钥：sk-or-v1-123456789
- 确保本地模型服务正在运行并可访问

## 🚀 快速部署

### 方法一：使用自动化脚本（推荐）

#### Linux/macOS
```bash
# 给脚本执行权限
chmod +x deploy-local-model.sh

# 运行部署脚本
./deploy-local-model.sh
```

#### Windows
```cmd
# 直接运行批处理文件
deploy-local-model.bat
```

### 方法二：手动部署

#### 1. 检查配置文件
确保以下文件存在并配置正确：
- `.env` - 环境变量配置
- `config.yaml` - AI服务配置
- `docker-compose.local-model.yaml` - Docker编排配置

#### 2. 创建数据目录
```bash
mkdir -p data
```

#### 3. 启动服务
```bash
# 停止现有服务
docker-compose -f docker-compose.local-model.yaml down

# 拉取最新镜像
docker-compose -f docker-compose.local-model.yaml pull

# 启动服务
docker-compose -f docker-compose.local-model.yaml up -d
```

#### 4. 验证部署
```bash
# 检查服务状态
docker-compose -f docker-compose.local-model.yaml ps

# 查看日志
docker-compose -f docker-compose.local-model.yaml logs -f
```

## 🔧 配置说明

### 环境变量配置 (.env)
```bash
# 本地模型API配置
OPENAI_API_KEY=sk-or-v1-123456789

# 模型名称
GENERATION_MODEL=Qwen3-235B-A22B

# 服务端口
HOST_PORT=3000
AI_SERVICE_FORWARD_PORT=5555

# 关闭遥测（可选）
TELEMETRY_ENABLED=false
```

### AI服务配置 (config.yaml)
```yaml
type: llm
provider: litellm_llm
models:
  - api_base: http://************:8000/v1
    model: openai/Qwen3-235B-A22B
    alias: default
    timeout: 600
    kwargs:
      max_tokens: 5000
      temperature: 0.2
```

### 网络配置
Docker容器通过以下方式访问本地模型：
- `extra_hosts` 配置：映射 `local-model-server:************`
- 网络桥接：允许容器访问宿主机网络

## 🔍 验证和测试

### 自动化测试
```bash
# 运行测试脚本
python3 test-local-model.py
```

### 手动验证

#### 1. 检查服务状态
```bash
# 检查所有服务
docker-compose -f docker-compose.local-model.yaml ps

# 检查特定服务日志
docker-compose -f docker-compose.local-model.yaml logs wren-ai-service
```

#### 2. 测试API连接
```bash
# 测试本地模型服务
curl -X GET "http://************:8000/v1/models" \
     -H "Authorization: Bearer sk-or-v1-123456789"

# 测试WrenAI服务
curl -X GET "http://localhost:5555/health"

# 测试UI服务
curl -X GET "http://localhost:3000"
```

#### 3. 测试模型对话
```bash
curl -X POST "http://************:8000/v1/chat/completions" \
     -H "Authorization: Bearer sk-or-v1-123456789" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "Qwen3-235B-A22B",
       "messages": [{"role": "user", "content": "你好"}],
       "temperature": 0.2,
       "max_tokens": 100
     }'
```

## 📊 服务访问

部署成功后，您可以通过以下地址访问服务：

- **WrenAI UI**: http://localhost:3000
- **AI服务API**: http://localhost:5555
- **本地模型API**: http://************:8000/v1

## 🛠️ 常用命令

### 服务管理
```bash
# 查看服务状态
docker-compose -f docker-compose.local-model.yaml ps

# 查看实时日志
docker-compose -f docker-compose.local-model.yaml logs -f

# 重启特定服务
docker-compose -f docker-compose.local-model.yaml restart wren-ai-service

# 停止所有服务
docker-compose -f docker-compose.local-model.yaml down

# 完全清理（包括数据卷）
docker-compose -f docker-compose.local-model.yaml down -v
```

### 调试命令
```bash
# 进入AI服务容器
docker-compose -f docker-compose.local-model.yaml exec wren-ai-service bash

# 查看容器网络配置
docker-compose -f docker-compose.local-model.yaml exec wren-ai-service cat /etc/hosts

# 测试容器内网络连接
docker-compose -f docker-compose.local-model.yaml exec wren-ai-service curl http://************:8000/v1/models
```

## 🔧 故障排除

### 常见问题

#### 1. 无法连接到本地模型服务
**症状**: AI服务日志显示连接************:8000失败

**解决方案**:
- 确认本地模型服务正在运行
- 检查防火墙设置
- 验证IP地址和端口是否正确

#### 2. AI服务启动失败
**症状**: wren-ai-service容器无法启动

**解决方案**:
```bash
# 查看详细日志
docker-compose -f docker-compose.local-model.yaml logs wren-ai-service

# 检查配置文件
cat config.yaml

# 验证环境变量
docker-compose -f docker-compose.local-model.yaml exec wren-ai-service env | grep OPENAI
```

#### 3. UI无法访问
**症状**: 浏览器无法打开http://localhost:3000

**解决方案**:
- 检查端口是否被占用：`netstat -an | grep 3000`
- 查看UI服务日志：`docker-compose -f docker-compose.local-model.yaml logs wren-ui`
- 确认服务已完全启动

### 日志分析
```bash
# 查看所有服务日志
docker-compose -f docker-compose.local-model.yaml logs

# 只查看错误日志
docker-compose -f docker-compose.local-model.yaml logs | grep -i error

# 实时监控特定服务
docker-compose -f docker-compose.local-model.yaml logs -f wren-ai-service
```

## 📝 注意事项

1. **网络配置**: 确保Docker容器能够访问************:8000
2. **资源要求**: 本地模型服务需要足够的计算资源
3. **API兼容性**: 确保本地模型服务支持OpenAI兼容的API格式
4. **安全性**: 在生产环境中，请使用更安全的API密钥管理方式
5. **监控**: 建议设置适当的监控和日志记录

## 🆘 获取帮助

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 运行测试脚本进行诊断
3. 检查服务日志获取详细错误信息
4. 确认本地模型服务的状态和配置
