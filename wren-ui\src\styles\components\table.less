// override antd styles
.ant-table {
  border: 1px @gray-4 solid;

  .ant-table-row:last-child .ant-table-cell {
    border-bottom: none;
  }

  &.ant-table-empty {
    .ant-table-body {
      overflow: auto !important;
    }

    .ant-table-tbody .ant-table-cell {
      border-bottom: none;
    }
  }

  .ant-table-expanded-row {
    > .ant-table-cell {
      background-color: @gray-3;
    }
  }

  .adm-nested-table {
    .ant-table {
      border: none;
      border-radius: 0;

      .ant-table-thead > tr > th {
        background: @gray-2;
      }

      .ant-table-tbody > tr.ant-table-row:hover > td,
      .ant-table-tbody > tr > td.ant-table-cell-row-hover {
        background: @gray-2;
      }
    }
  }

  &--text-sm {
    .ant-table {
      font-size: @font-size-sm !important;
    }
  }
}

.ant-table-wrapper:not(.ant-table-has-header) {
  .ant-table-empty {
    border: none;

    .ant-empty-normal {
      margin: 80px 0;
    }
  }
}
