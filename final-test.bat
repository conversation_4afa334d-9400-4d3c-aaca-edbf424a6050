@echo off
echo ========================================
echo 🎯 WrenAI 最终系统测试
echo ========================================
echo.

echo 📋 1. 检查Docker容器状态
docker-compose -f docker-compose.local-model.yaml ps
echo.

echo 📋 2. 测试Web界面访问
powershell -Command "try { $response = Invoke-WebRequest -Uri http://localhost:3000 -Method Head -TimeoutSec 5; Write-Host '✅ Web界面访问正常 (状态码:' $response.StatusCode ')' } catch { Write-Host '❌ Web界面访问失败:' $_.Exception.Message }"
echo.

echo 📋 3. 测试本地LLM连接
powershell -Command "try { $response = Invoke-WebRequest -Uri http://************:8000/v1/models -Method Get -TimeoutSec 5; Write-Host '✅ 本地LLM连接正常' } catch { Write-Host '❌ 本地LLM连接失败:' $_.Exception.Message }"
echo.

echo 📋 4. 检查服务日志（最近10行）
echo --- wren-ui 日志 ---
docker logs wrenai-wren-ui-1 --tail 10 2>nul
echo.
echo --- ibis-server 日志 ---
docker logs wrenai-ibis-server-1 --tail 10 2>nul
echo.

echo 📋 5. 网络连接测试
docker exec wrenai-ibis-server-1 python3 -c "
import socket
def test_conn(host, port, name):
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        print(f'{'✅' if result == 0 else '❌'} {name}: {host}:{port} {'成功' if result == 0 else '失败'}')
    except Exception as e:
        print(f'❌ {name}: 异常 {e}')

test_conn('************', 5432, 'PostgreSQL数据库')
test_conn('************', 8000, '本地LLM服务')
"
echo.

echo ========================================
echo 🎉 测试完成！
echo.
echo 💡 使用说明：
echo    1. 打开浏览器访问: http://localhost:3000
echo    2. 在WrenAI中配置数据源时使用: ************:5432
echo    3. 数据库连接信息: 用户名=root, 密码=yeestor, 数据库=yeestor
echo    4. LLM已配置为本地Qwen3-235B-A22B模型
echo ========================================
pause
