@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM WrenAI快速部署脚本 (不拉取镜像)
echo 🚀 快速部署WrenAI本地模型服务...

REM 检查Docker
docker version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未运行，请先启动Docker Desktop
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "config.yaml" (
    echo ❌ config.yaml不存在
    pause
    exit /b 1
)

if not exist ".env" (
    echo ❌ .env不存在
    pause
    exit /b 1
)

REM 创建数据目录
if not exist "data" mkdir data

REM 停止现有服务
echo 🛑 停止现有服务...
docker-compose -f docker-compose.local-model.yaml down --remove-orphans >nul 2>&1

REM 直接启动服务（不拉取镜像）
echo 🚀 启动服务...
docker-compose -f docker-compose.local-model.yaml up -d

if errorlevel 1 (
    echo ❌ 服务启动失败
    echo 💡 提示: 如果是镜像不存在的问题，请运行:
    echo    docker-compose -f docker-compose.local-model.yaml pull
    pause
    exit /b 1
)

echo ✅ 服务启动成功
echo 📊 访问地址: http://localhost:3000
pause
