type: llm
provider: litellm_llm
models:
- model: gpt-4.1-nano-2025-04-14
  alias: default
  kwargs:
    max_tokens: 4096
    n: 1
    seed: 0
    temperature: 0
  timeout: 120

---
type: embedder
provider: litellm_embedder
models:
- model: text-embedding-3-large
  alias: default
  timeout: 120

---
type: engine
provider: wren_ui
endpoint: http://localhost:3000

---
type: document_store
provider: qdrant
location: http://localhost:6333
embedding_model_dim: 3072
timeout: 120

---
type: pipeline
pipes:
  - name: indexing
    embedder: litellm_embedder.default
    document_store: qdrant
  - name: retrieval
    llm: litellm_llm.default
    embedder: litellm_embedder.default
    document_store: qdrant
  - name: historical_question_retrieval
    embedder: litellm_embedder.default
    document_store: qdrant
  - name: sql_generation
    llm: litellm_llm.default
    engine: wren_ui
    document_store: qdrant
  - name: sql_correction
    llm: litellm_llm.default
    engine: wren_ui
    document_store: qdrant
  - name: followup_sql_generation
    llm: litellm_llm.default
    engine: wren_ui
    document_store: qdrant
  - name: sql_answer
    llm: litellm_llm.default
    engine: wren_ui
  - name: sql_explanation
    llm: litellm_llm.default
  - name: sql_regeneration
    llm: litellm_llm.default
    engine: wren_ui
  - name: semantics_description
    llm: litellm_llm.default
  - name: relationship_recommendation
    llm: litellm_llm.default
    engine: wren_ui
  - name: user_guide_assistance
    llm: litellm_llm.default
  - name: data_assistance
    llm: litellm_llm.default
    
---
settings:
  host: 127.0.0.1
  port: 5556
  column_indexing_batch_size: 50
  doc_endpoint: https://docs.getwren.ai
  is_oss: true
  table_retrieval_size: 10
  table_column_retrieval_size: 1000
  query_cache_maxsize: 1000
  query_cache_ttl: 3600
  langfuse_host: https://cloud.langfuse.com
  langfuse_enable: false
  logging_level: INFO
  development: false
