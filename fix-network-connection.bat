@echo off
chcp 65001 >nul
echo 🔧 修复WrenAI数据库网络连接问题
echo.

echo 📋 当前问题:
echo    - PostgreSQL服务器: ************:5432
echo    - Docker容器无法连接到外部数据库
echo    - 错误: "server closed the connection unexpectedly"

echo.
echo 🔍 解决方案: 使用host网络模式

echo.
echo 1️⃣ 停止当前服务...
docker-compose -f docker-compose.local-model.yaml down

echo.
echo 2️⃣ 备份当前配置...
if not exist docker-compose.local-model.yaml.backup (
    copy docker-compose.local-model.yaml docker-compose.local-model.yaml.backup >nul
    echo ✅ 配置已备份
) else (
    echo ℹ️  配置备份已存在
)

echo.
echo 3️⃣ 应用host网络模式修复...

REM 创建临时修复文件
echo # 临时修复配置 - 使用host网络模式 > temp-fix.yaml
echo version: '3.8' >> temp-fix.yaml
echo. >> temp-fix.yaml
echo volumes: >> temp-fix.yaml
echo   data: >> temp-fix.yaml
echo. >> temp-fix.yaml
echo services: >> temp-fix.yaml
echo   bootstrap: >> temp-fix.yaml
echo     image: ghcr.io/canner/wren-bootstrap:${WREN_BOOTSTRAP_VERSION} >> temp-fix.yaml
echo     restart: on-failure >> temp-fix.yaml
echo     platform: ${PLATFORM} >> temp-fix.yaml
echo     environment: >> temp-fix.yaml
echo       DATA_PATH: /app/data >> temp-fix.yaml
echo     volumes: >> temp-fix.yaml
echo       - data:/app/data >> temp-fix.yaml
echo     command: /bin/sh /app/init.sh >> temp-fix.yaml
echo. >> temp-fix.yaml
echo   ibis-server: >> temp-fix.yaml
echo     image: ghcr.io/canner/wren-engine-ibis:${IBIS_SERVER_VERSION} >> temp-fix.yaml
echo     restart: on-failure >> temp-fix.yaml
echo     platform: ${PLATFORM} >> temp-fix.yaml
echo     network_mode: host >> temp-fix.yaml
echo     environment: >> temp-fix.yaml
echo       WREN_ENGINE_ENDPOINT: http://localhost:${WREN_ENGINE_PORT} >> temp-fix.yaml
echo     volumes: >> temp-fix.yaml
echo       - ${LOCAL_STORAGE:-.}:/usr/src/app/data >> temp-fix.yaml

echo.
echo 4️⃣ 启动修复后的服务...
docker-compose -f temp-fix.yaml up -d bootstrap
timeout /t 5 /nobreak >nul
docker-compose -f temp-fix.yaml up -d ibis-server

echo.
echo 5️⃣ 等待服务启动...
timeout /t 10 /nobreak >nul

echo.
echo 6️⃣ 测试数据库连接...
python test-db-connection.py

echo.
echo ========================================
echo 🎉 网络修复完成！
echo.
echo 💡 说明:
echo    - ibis-server现在使用host网络模式
echo    - 可以直接访问宿主机网络上的PostgreSQL服务器
echo    - 如果测试成功，您可以在WrenAI中配置数据源
echo.
echo 📝 数据库连接信息:
echo    - 主机: ************
echo    - 端口: 5432
echo    - 数据库: yeestor
echo    - 用户名: root
echo    - 密码: yeestor
echo ========================================
pause
