@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🔍 检查Docker Desktop状态...

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未安装或未添加到PATH
    echo    请安装Docker Desktop: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo ✅ Docker已安装

REM 检查Docker服务是否运行
docker version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Docker Desktop未运行
    echo 🚀 正在尝试启动Docker Desktop...
    
    REM 尝试启动Docker Desktop
    start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    
    echo ⏳ 等待Docker Desktop启动...
    set /a counter=0
    :wait_docker
    set /a counter+=1
    timeout /t 5 /nobreak >nul
    docker version >nul 2>&1
    if not errorlevel 1 (
        echo ✅ Docker Desktop启动成功
        goto docker_ready
    )
    if !counter! geq 24 (
        echo ❌ Docker Desktop启动超时（2分钟）
        echo    请手动启动Docker Desktop并等待其完全启动
        pause
        exit /b 1
    )
    echo    等待中... (!counter!/24)
    goto wait_docker
) else (
    echo ✅ Docker Desktop正在运行
)

:docker_ready
echo 📊 Docker信息:
docker version --format "   Client: {{.Client.Version}}"
docker version --format "   Server: {{.Server.Version}}"

echo.
echo 🎉 Docker Desktop已就绪，可以开始部署WrenAI
echo.
pause
