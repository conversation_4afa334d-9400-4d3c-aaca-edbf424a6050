# To generate valid certificates into wren-ui.myhost.net-tls
# Dependancy: install https://cert-manager.io
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wren-ui.myhost.net
spec:
  dnsNames:
    - wren-ui.myhost.net
  issuerRef:
    group: cert-manager.io
    kind: ClusterIssuer
    ### Replace with the name of your issuer, otherwise the secret will be produce randoom name and the ingress will not work.
    name: myhost.net-prod
  ### Your ingress will be looking for this exact name:
  secretName: wren-ui.myhost.net-tls
